import { PageHeader } from '../components/shared'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '../components/ui/card'
import { Badge } from '../components/ui/badge'
import { Button } from '../components/ui/button'
import { Input } from '../components/ui/input'
import { Label } from '../components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '../components/ui/select'
import { useLanguage, type Language } from '../contexts/LanguageContext'

export function SettingsPage() {
  const { language, setLanguage, t } = useLanguage()

  const handleLanguageChange = (newLanguage: Language) => {
    setLanguage(newLanguage)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <PageHeader title={t('settings.title')} description={t('settings.description')} />

      {/* General Settings */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.general')}</CardTitle>
          <CardDescription>{t('settings.generalDescription')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="username">{t('settings.displayName')}</Label>
              <Input id="username" placeholder={t('settings.displayName')} defaultValue="User" />
            </div>

            <div className="space-y-2">
              <Label htmlFor="theme">{t('settings.theme')}</Label>
              <Select defaultValue="system">
                <SelectTrigger>
                  <SelectValue placeholder={t('settings.selectTheme')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">{t('settings.themes.light')}</SelectItem>
                  <SelectItem value="dark">{t('settings.themes.dark')}</SelectItem>
                  <SelectItem value="system">{t('settings.themes.system')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="language">{t('settings.language')}</Label>
            <Select value={language} onValueChange={handleLanguageChange}>
              <SelectTrigger className="w-full md:w-48">
                <SelectValue placeholder={t('settings.selectLanguage')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="zh">{t('settings.languages.zh')}</SelectItem>
                <SelectItem value="en">{t('settings.languages.en')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* P.A.R.A. Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {t('settings.para.title')}
            <Badge variant="secondary">{t('settings.para.badge')}</Badge>
          </CardTitle>
          <CardDescription>{t('settings.para.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.para.autoArchive.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.para.autoArchive.description')}
                </p>
              </div>
              <div className="flex items-center gap-2">
                <Input type="number" defaultValue="30" className="w-20" />
                <span className="text-sm text-muted-foreground">{t('settings.para.autoArchive.unit')}</span>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.para.weeklyReview.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.para.weeklyReview.description')}
                </p>
              </div>
              <Select defaultValue="sunday">
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="sunday">{t('settings.para.weeklyReview.days.sunday')}</SelectItem>
                  <SelectItem value="monday">{t('settings.para.weeklyReview.days.monday')}</SelectItem>
                  <SelectItem value="friday">{t('settings.para.weeklyReview.days.friday')}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.para.projectTemplate.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.para.projectTemplate.description')}
                </p>
              </div>
              <Select defaultValue="basic">
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">{t('settings.para.projectTemplate.options.basic')}</SelectItem>
                  <SelectItem value="detailed">{t('settings.para.projectTemplate.options.detailed')}</SelectItem>
                  <SelectItem value="agile">{t('settings.para.projectTemplate.options.agile')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Data & Storage */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.dataStorage.title')}</CardTitle>
          <CardDescription>{t('settings.dataStorage.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">2.4 MB</div>
              <p className="text-sm text-muted-foreground">{t('settings.dataStorage.stats.databaseSize')}</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">156</div>
              <p className="text-sm text-muted-foreground">{t('settings.dataStorage.stats.totalItems')}</p>
            </div>

            <div className="text-center p-4 border rounded-lg">
              <div className="text-2xl font-bold">12</div>
              <p className="text-sm text-muted-foreground">{t('settings.dataStorage.stats.backups')}</p>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-2">
            <Button variant="outline" className="flex-1">
              {t('settings.dataStorage.actions.exportData')}
            </Button>
            <Button variant="outline" className="flex-1">
              {t('settings.dataStorage.actions.importData')}
            </Button>
            <Button variant="outline" className="flex-1">
              {t('settings.dataStorage.actions.createBackup')}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.advanced.title')}</CardTitle>
          <CardDescription>{t('settings.advanced.description')}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.advanced.debugMode.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.advanced.debugMode.description')}
                </p>
              </div>
              <Badge variant="outline">{t('settings.status.disabled')}</Badge>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.advanced.autoUpdate.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.advanced.autoUpdate.description')}
                </p>
              </div>
              <Badge variant="secondary">{t('settings.status.enabled')}</Badge>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">{t('settings.advanced.analytics.title')}</h4>
                <p className="text-sm text-muted-foreground">
                  {t('settings.advanced.analytics.description')}
                </p>
              </div>
              <Badge variant="secondary">{t('settings.status.enabled')}</Badge>
            </div>
          </div>

          <div className="pt-4 border-t">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button variant="outline">{t('settings.advanced.actions.resetSettings')}</Button>
              <Button variant="outline">{t('settings.advanced.actions.clearCache')}</Button>
              <Button variant="destructive">{t('settings.advanced.actions.resetAllData')}</Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* About */}
      <Card>
        <CardHeader>
          <CardTitle>{t('settings.about.title')}</CardTitle>
          <CardDescription>{t('settings.about.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p>
              <strong>{t('settings.about.info.version')}</strong> 1.0.0
            </p>
            <p>
              <strong>{t('settings.about.info.build')}</strong> 2025.01.14
            </p>
            <p>
              <strong>{t('settings.about.info.electron')}</strong> 28.0.0
            </p>
            <p>
              <strong>{t('settings.about.info.nodejs')}</strong> 18.18.2
            </p>
            <p>
              <strong>{t('settings.about.info.license')}</strong> MIT
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default SettingsPage
