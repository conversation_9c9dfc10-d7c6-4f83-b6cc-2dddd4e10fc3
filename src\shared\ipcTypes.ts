// IPC communication types

import type {
  <PERSON><PERSON><PERSON>ationR<PERSON>ult,
  <PERSON>Info,
  FileContent,
  FileSystemConfig,
  FileSystemEvent
} from './fileTypes'
import type { DatabaseResult, DatabaseConfig } from './types'

// IPC Channel names
export const IPC_CHANNELS = {
  // Database operations
  DB_INITIALIZE: 'db:initialize',
  DB_TEST_CONNECTION: 'db:test-connection',
  DB_CREATE_PROJECT: 'db:create-project',
  DB_GET_PROJECTS: 'db:get-projects',
  DB_GET_PROJECT_BY_ID: 'db:get-project-by-id',
  DB_UPDATE_PROJECT: 'db:update-project',
  DB_DELETE_PROJECT: 'db:delete-project',
  DB_ARCHIVE_PROJECT: 'db:archive-project',
  DB_CREATE_AREA: 'db:create-area',
  DB_GET_AREAS: 'db:get-areas',
  DB_GET_AREA_BY_ID: 'db:get-area-by-id',
  DB_UPDATE_AREA: 'db:update-area',
  DB_DELETE_AREA: 'db:delete-area',
  DB_ARCHIVE_AREA: 'db:archive-area',
  DB_GET_ARCHIVED_PROJECTS: 'db:get-archived-projects',
  DB_GET_ARCHIVED_AREAS: 'db:get-archived-areas',
  DB_RESTORE_PROJECT: 'db:restore-project',
  DB_RESTORE_AREA: 'db:restore-area',
  DB_CREATE_TASK: 'db:create-task',
  DB_GET_TASKS: 'db:get-tasks',
  DB_UPDATE_TASK: 'db:update-task',
  DB_CREATE_RESOURCE: 'db:create-resource',
  DB_GET_RESOURCES: 'db:get-resources',
  DB_GET_PROJECT_RESOURCES: 'db:get-project-resources',
  DB_LINK_RESOURCE_TO_PROJECT: 'db:link-resource-to-project',
  DB_UNLINK_RESOURCE_FROM_PROJECT: 'db:unlink-resource-from-project',
  DB_GET_AREA_RESOURCES: 'db:get-area-resources',
  DB_UNLINK_RESOURCE_FROM_AREA: 'db:unlink-resource-from-area',
  // ProjectKPI operations
  DB_CREATE_PROJECT_KPI: 'db:create-project-kpi',
  DB_GET_PROJECT_KPIS: 'db:get-project-kpis',
  DB_UPDATE_PROJECT_KPI: 'db:update-project-kpi',
  DB_DELETE_PROJECT_KPI: 'db:delete-project-kpi',

  // KPI Record operations
  DB_CREATE_KPI_RECORD: 'db:create-kpi-record',
  DB_GET_KPI_RECORDS: 'db:get-kpi-records',
  DB_UPDATE_KPI_RECORD: 'db:update-kpi-record',
  DB_DELETE_KPI_RECORD: 'db:delete-kpi-record',

  // Area Metric operations
  DB_CREATE_AREA_METRIC: 'db:create-area-metric',
  DB_GET_AREA_METRICS: 'db:get-area-metrics',
  DB_UPDATE_AREA_METRIC: 'db:update-area-metric',
  DB_DELETE_AREA_METRIC: 'db:delete-area-metric',

  // Area Metric Record operations
  DB_CREATE_AREA_METRIC_RECORD: 'db:create-area-metric-record',
  DB_GET_AREA_METRIC_RECORDS: 'db:get-area-metric-records',
  DB_UPDATE_AREA_METRIC_RECORD: 'db:update-area-metric-record',
  DB_DELETE_AREA_METRIC_RECORD: 'db:delete-area-metric-record',

  // Habit operations
  DB_GET_HABITS_BY_AREA: 'db:get-habits-by-area',

  // Deliverable operations
  DB_CREATE_DELIVERABLE: 'db:create-deliverable',
  DB_GET_PROJECT_DELIVERABLES: 'db:get-project-deliverables',
  DB_UPDATE_DELIVERABLE: 'db:update-deliverable',
  DB_DELETE_DELIVERABLE: 'db:delete-deliverable',
  // Habit operations
  DB_CREATE_HABIT: 'db:create-habit',
  DB_UPDATE_HABIT: 'db:update-habit',
  DB_DELETE_HABIT: 'db:delete-habit',
  // Habit record operations
  DB_CREATE_HABIT_RECORD: 'db:create-habit-record',
  DB_UPDATE_HABIT_RECORD: 'db:update-habit-record',
  DB_GET_HABIT_RECORDS: 'db:get-habit-records',
  DB_DELETE_HABIT_RECORD: 'db:delete-habit-record',

  // {{ AURA-X: Add - 定期维护任务IPC通道. Approval: 寸止(ID:1738157400). }}
  // Recurring task operations
  DB_CREATE_RECURRING_TASK: 'db:create-recurring-task',
  DB_GET_RECURRING_TASKS: 'db:get-recurring-tasks',
  DB_UPDATE_RECURRING_TASK: 'db:update-recurring-task',
  DB_DELETE_RECURRING_TASK: 'db:delete-recurring-task',

  // Document Link operations
  DB_CREATE_DOCUMENT_LINK: 'db:create-document-link',
  DB_GET_DOCUMENT_LINKS: 'db:get-document-links',
  DB_GET_BACKLINKS: 'db:get-backlinks',
  DB_GET_OUTLINKS: 'db:get-outlinks',
  DB_UPDATE_DOCUMENT_LINK: 'db:update-document-link',
  DB_DELETE_DOCUMENT_LINK: 'db:delete-document-link',
  DB_DELETE_DOCUMENT_LINKS: 'db:delete-document-links',
  DB_UPDATE_DOCUMENT_PATH: 'db:update-document-path',
  DB_MARK_LINKS_INVALID: 'db:mark-links-invalid',
  DB_MARK_LINKS_VALID: 'db:mark-links-valid',
  DB_GET_LINK_STATISTICS: 'db:get-link-statistics',
  DB_SEARCH_LINKS: 'db:search-links',
  DB_REPLACE_DOCUMENT_LINKS: 'db:replace-document-links',

  // Checklist operations
  DB_CREATE_CHECKLIST: 'db:create-checklist',
  DB_GET_CHECKLISTS: 'db:get-checklists',
  DB_UPDATE_CHECKLIST: 'db:update-checklist',
  DB_DELETE_CHECKLIST: 'db:delete-checklist',

  // Checklist Instance operations
  DB_CREATE_CHECKLIST_INSTANCE: 'db:create-checklist-instance',
  DB_GET_CHECKLIST_INSTANCES: 'db:get-checklist-instances',
  DB_UPDATE_CHECKLIST_INSTANCE: 'db:update-checklist-instance',
  DB_DELETE_CHECKLIST_INSTANCE: 'db:delete-checklist-instance',

  // File system operations
  FS_INITIALIZE: 'fs:initialize',
  FS_REINITIALIZE: 'fs:reinitialize',
  FS_READ_FILE: 'fs:read-file',
  FS_WRITE_FILE: 'fs:write-file',
  FS_DELETE_FILE: 'fs:delete-file',
  FS_MOVE_FILE: 'fs:move-file',
  FS_COPY_FILE: 'fs:copy-file',
  FS_FILE_EXISTS: 'fs:file-exists',
  FS_GET_FILE_INFO: 'fs:get-file-info',
  FS_LIST_DIRECTORY: 'fs:list-directory',
  FS_CREATE_DIRECTORY: 'fs:create-directory',
  FS_RENAME: 'fs:rename',
  FS_DELETE_DIRECTORY: 'fs:delete-directory',
  FS_WATCH_DIRECTORY: 'fs:watch-directory',
  FS_UNWATCH_DIRECTORY: 'fs:unwatch-directory',

  // File system events (from main to renderer)
  FS_EVENT: 'fs:event',
  FS_FILE_CHANGED: 'fs:file-changed',
  FS_FILE_CREATED: 'fs:file-created',
  FS_FILE_DELETED: 'fs:file-deleted',

  // Settings operations
  SETTINGS_GET_USER_SETTINGS: 'settings:get-user-settings',
  SETTINGS_UPDATE_USER_SETTINGS: 'settings:update-user-settings',
  SETTINGS_RESET_USER_SETTINGS: 'settings:reset-user-settings',
  SETTINGS_EXPORT_DATA: 'settings:export-data',
  SETTINGS_IMPORT_DATA: 'settings:import-data',
  SETTINGS_CREATE_BACKUP: 'settings:create-backup',
  SETTINGS_GET_DATABASE_INFO: 'settings:get-database-info',
  SETTINGS_SELECT_RESOURCE_PATH: 'settings:select-resource-path',
  SETTINGS_UPDATE_RESOURCE_PATH: 'settings:update-resource-path',

  // Application operations
  APP_GET_VERSION: 'app:get-version',
  APP_GET_PATH: 'app:get-path',
  APP_SHOW_MESSAGE_BOX: 'app:show-message-box',
  APP_SHOW_ERROR_BOX: 'app:show-error-box',
  APP_SHOW_OPEN_DIALOG: 'app:show-open-dialog',
  APP_SHOW_SAVE_DIALOG: 'app:show-save-dialog',

  // Window operations
  WINDOW_MINIMIZE: 'window:minimize',
  WINDOW_MAXIMIZE: 'window:maximize',
  WINDOW_CLOSE: 'window:close',
  WINDOW_TOGGLE_DEVTOOLS: 'window:toggle-devtools'
} as const

// Database IPC types
export interface DatabaseIpcRequest {
  channel: string
  data?: any
}

export interface DatabaseIpcResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

// File system IPC types
export interface FileSystemIpcRequest {
  channel: string
  path?: string
  content?: string
  options?: any
}

export interface FileSystemIpcResponse<T = any> extends FileOperationResult<T> {}

// Application IPC types
export interface AppIpcRequest {
  channel: string
  data?: any
}

export interface AppIpcResponse<T = any> {
  success: boolean
  data?: T
  error?: string
}

// Specific operation types
export interface CreateProjectRequest {
  name: string
  description?: string
  goal?: string
  areaId?: string
}

export interface UpdateProjectRequest {
  id: string
  updates: {
    name?: string
    description?: string
    goal?: string
    status?: string
    progress?: number
    startDate?: Date
    deadline?: Date
    archived?: boolean
    areaId?: string | null  // {{ AURA-X: Add - 支持更新项目的领域关联. Approval: 寸止(ID:1738157400). }}
    deliverable?: string | null
  }
}

export interface UpdateAreaRequest {
  id: string
  updates: {
    name?: string
    description?: string | null
    standard?: string | null
    status?: string
    reviewFrequency?: string
    color?: string | null
    icon?: string | null
    archived?: boolean
  }
}

export interface CreateAreaRequest {
  name: string
  description?: string
  color?: string
  icon?: string
  // {{ AURA-X: Add - 支持完整的Area字段. Approval: 寸止(ID:1738157400). }}
  standard?: string
  status?: string
  reviewFrequency?: string
  archived?: boolean
}

export interface CreateTaskRequest {
  content: string
  description?: string
  priority?: string
  deadline?: Date
  projectId?: string
  areaId?: string
  parentId?: string
  // 资源关联字段
  sourceResourceId?: string
  sourceText?: string
  sourceContext?: string
}

export interface UpdateTaskRequest {
  id: string
  updates: {
    content?: string
    description?: string
    completed?: boolean
    priority?: string
    deadline?: Date
    // Enhanced task attributes
    estimatedHours?: number
    actualHours?: number
    startedAt?: Date
    completedAt?: Date
    status?: string
    progress?: number
    blockedBy?: string
    dependencies?: string[]
    parentId?: string
    projectId?: string
    areaId?: string
    deliverableId?: string
  }
}

export interface CreateResourceRequest {
  resourcePath: string
  title?: string
  projectId?: string
  areaId?: string
}

export interface CreateProjectKPIRequest {
  projectId: string
  name: string
  value: string
  target?: string
  unit?: string
  frequency?: string
  // {{ AURA-X: Add - 添加direction字段支持双向KPI. Approval: 寸止(ID:1738157400). }}
  direction?: string
}

export interface UpdateProjectKPIRequest {
  id: string
  updates: {
    name?: string
    value?: string
    target?: string
    unit?: string
    frequency?: string
    // {{ AURA-X: Add - 添加direction字段支持双向KPI. Approval: 寸止(ID:1738157400). }}
    direction?: string
  }
}

export interface CreateKPIRecordRequest {
  kpiId: string
  value: string
  note?: string
  recordedAt?: Date
}

export interface UpdateKPIRecordRequest {
  id: string
  updates: {
    value?: string
    note?: string
    recordedAt?: Date
  }
}

export interface CreateAreaMetricRequest {
  areaId: string
  name: string
  value: string
  target?: string
  unit?: string
  frequency?: string
  // {{ AURA-X: Add - 扩展创建请求类型支持新字段. Approval: 寸止(ID:1738157400). }}
  trackingType?: string
  habitConfig?: any
  standardConfig?: any
  isActive?: boolean
  priority?: string
  category?: string
  description?: string
  // {{ AURA-X: Add - 添加direction字段支持双向KPI. Approval: 寸止(ID:1738157400). }}
  direction?: string
}

export interface UpdateAreaMetricRequest {
  id: string
  updates: {
    name?: string
    value?: string
    target?: string
    unit?: string
    frequency?: string
    // {{ AURA-X: Add - 扩展更新请求类型支持新字段. Approval: 寸止(ID:1738157400). }}
    trackingType?: string
    habitConfig?: any
    standardConfig?: any
    isActive?: boolean
    priority?: string
    category?: string
    description?: string
    // {{ AURA-X: Add - 添加direction字段支持双向KPI. Approval: 寸止(ID:1738157400). }}
    direction?: string
  }
}

export interface CreateAreaMetricRecordRequest {
  metricId: string
  value: string
  note?: string
  recordedAt?: Date
  // {{ AURA-X: Add - 扩展记录创建请求类型支持新字段. Approval: 寸止(ID:1738157400). }}
  mood?: string
  energy?: string
  context?: any
  tags?: string[]
  quality?: string
  duration?: number
  difficulty?: string
}

export interface UpdateAreaMetricRecordRequest {
  id: string
  updates: {
    value?: string
    note?: string
    recordedAt?: Date
  }
}

export interface CreateDeliverableRequest {
  projectId: string
  title: string
  description?: string
  type?: string // document, milestone, metric, artifact
  status?: string // planned, in_progress, completed, delivered, accepted
  priority?: string // low, medium, high, critical
  plannedDate?: Date
  deadline?: Date
  content?: string
  attachments?: any
  metrics?: any
}

export interface UpdateDeliverableRequest {
  id: string
  updates: {
    title?: string
    description?: string
    type?: string
    status?: string
    priority?: string
    plannedDate?: Date
    actualDate?: Date
    deadline?: Date
    content?: string
    attachments?: any
    metrics?: any
  }
}

export interface CreateHabitRecordRequest {
  habitId: string
  date: Date
  completed?: boolean
  value?: number
  note?: string
}

export interface CreateHabitRequest {
  name: string
  areaId: string
  description?: string
  frequency?: string
  target?: number
}

export interface UpdateHabitRequest {
  id: string
  updates: {
    name?: string
    description?: string
    frequency?: string
    target?: number
  }
}

export interface UpdateHabitRecordRequest {
  id: string
  updates: {
    completed?: boolean
    value?: number
    note?: string
  }
}

// {{ AURA-X: Add - 定期维护任务请求类型. Approval: 寸止(ID:1738157400). }}
export interface CreateRecurringTaskRequest {
  title: string
  description?: string
  repeatRule: string
  repeatInterval?: number
  specificDay?: number
  nextDueDate: Date
  areaId: string
}

export interface UpdateRecurringTaskRequest {
  id: string
  updates: {
    title?: string
    description?: string
    repeatRule?: string
    repeatInterval?: number
    specificDay?: number
    nextDueDate?: Date
    lastCompletedDate?: Date
  }
}

export interface LinkResourceToProjectRequest {
  resourceId: string
  projectId: string
}

// Document Link request types
export interface CreateDocumentLinkRequest {
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType?: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  linkStrength?: number
}

export interface UpdateDocumentLinkRequest {
  id: string
  sourceDocTitle?: string
  targetDocTitle?: string
  linkText?: string
  displayText?: string
  startPosition?: number
  endPosition?: number
  lineNumber?: number
  columnNumber?: number
  contextBefore?: string
  contextAfter?: string
  isValid?: boolean
  linkStrength?: number
}

export interface DocumentLinkFilters {
  sourceDocPath?: string
  targetDocPath?: string
  linkType?: string
  isValid?: boolean
}

export interface UpdateDocumentPathRequest {
  oldPath: string
  newPath: string
  newTitle?: string
}

export interface SearchLinksRequest {
  query: string
  filters?: DocumentLinkFilters
}

export interface ReplaceDocumentLinksRequest {
  sourceDocPath: string
  links: CreateDocumentLinkRequest[]
}

// Checklist request types
export interface CreateChecklistRequest {
  name: string
  template: any[] // JSON array of checklist items
  areaId: string
}

export interface UpdateChecklistRequest {
  id: string
  updates: {
    name?: string
    template?: any[]
  }
}

export interface CreateChecklistInstanceRequest {
  checklistId: string
  status: any[] // JSON array of item statuses
  templateSnapshot?: {
    name: string
    createdAt: Date
  }
}

export interface UpdateChecklistInstanceRequest {
  id: string
  updates: {
    status?: any[]
    completedAt?: Date
    templateSnapshot?: {
      name: string
      createdAt: Date
    }
  }
}

export interface ReadFileRequest {
  path: string
  encoding?: BufferEncoding
}

export interface WriteFileRequest {
  path: string
  content: string | number[]
  encoding?: BufferEncoding | 'binary'
  createDirs?: boolean
  backup?: boolean
}

export interface MoveFileRequest {
  sourcePath: string
  targetPath: string
  overwrite?: boolean
  createDirs?: boolean
}

export interface CopyFileRequest {
  sourcePath: string
  targetPath: string
  overwrite?: boolean
  createDirs?: boolean
}

// Event types
export interface IpcEventData {
  type: string
  payload: any
  timestamp: Date
}

// API method signatures for type safety
export interface DatabaseApi {
  initialize(): Promise<DatabaseResult<DatabaseConfig>>
  testConnection(): Promise<DatabaseResult<boolean>>
  createProject(data: CreateProjectRequest): Promise<DatabaseResult<any>>
  getProjects(): Promise<DatabaseResult<any[]>>
  getProjectById(id: string, includeArchived?: boolean): Promise<DatabaseResult<any>>
  updateProject(data: UpdateProjectRequest): Promise<DatabaseResult<any>>
  deleteProject(id: string): Promise<DatabaseResult<void>>
  archiveProject(id: string): Promise<DatabaseResult<void>>
  createArea(data: CreateAreaRequest): Promise<DatabaseResult<any>>
  getAreas(): Promise<DatabaseResult<any[]>>
  getAreaById(id: string, includeArchived?: boolean): Promise<DatabaseResult<any>>
  updateArea(data: UpdateAreaRequest): Promise<DatabaseResult<any>>
  deleteArea(id: string): Promise<DatabaseResult<void>>
  archiveArea(id: string): Promise<DatabaseResult<void>>
  getArchivedProjects(): Promise<DatabaseResult<any[]>>
  getArchivedAreas(): Promise<DatabaseResult<any[]>>
  restoreProject(id: string): Promise<DatabaseResult<void>>
  restoreArea(id: string): Promise<DatabaseResult<void>>
  createTask(data: CreateTaskRequest): Promise<DatabaseResult<any>>
  getTasks(filters?: any): Promise<DatabaseResult<any[]>>
  updateTask(data: UpdateTaskRequest): Promise<DatabaseResult<any>>
  // ProjectKPI operations
  createProjectKPI(data: CreateProjectKPIRequest): Promise<DatabaseResult<any>>
  getProjectKPIs(projectId: string): Promise<DatabaseResult<any[]>>
  updateProjectKPI(data: UpdateProjectKPIRequest): Promise<DatabaseResult<any>>
  deleteProjectKPI(id: string): Promise<DatabaseResult<void>>
  // KPI Record operations
  createKPIRecord(data: CreateKPIRecordRequest): Promise<DatabaseResult<any>>
  getKPIRecords(kpiId: string, limit?: number): Promise<DatabaseResult<any[]>>
  updateKPIRecord(data: UpdateKPIRecordRequest): Promise<DatabaseResult<any>>
  deleteKPIRecord(id: string): Promise<DatabaseResult<void>>
  // Area Metric operations
  createAreaMetric(data: CreateAreaMetricRequest): Promise<DatabaseResult<any>>
  getAreaMetrics(areaId: string): Promise<DatabaseResult<any[]>>
  updateAreaMetric(data: UpdateAreaMetricRequest): Promise<DatabaseResult<any>>
  deleteAreaMetric(id: string): Promise<DatabaseResult<void>>
  // Area Metric Record operations
  createAreaMetricRecord(data: CreateAreaMetricRecordRequest): Promise<DatabaseResult<any>>
  getAreaMetricRecords(metricId: string, limit?: number): Promise<DatabaseResult<any[]>>
  updateAreaMetricRecord(data: UpdateAreaMetricRecordRequest): Promise<DatabaseResult<any>>
  deleteAreaMetricRecord(id: string): Promise<DatabaseResult<void>>
  // Deliverable operations
  createDeliverable(data: CreateDeliverableRequest): Promise<DatabaseResult<any>>
  getProjectDeliverables(projectId: string): Promise<DatabaseResult<any[]>>
  updateDeliverable(data: UpdateDeliverableRequest): Promise<DatabaseResult<any>>
  deleteDeliverable(id: string): Promise<DatabaseResult<void>>
  // Habit operations
  createHabit(data: CreateHabitRequest): Promise<DatabaseResult<any>>
  updateHabit(data: UpdateHabitRequest): Promise<DatabaseResult<any>>
  deleteHabit(id: string): Promise<DatabaseResult<void>>
  getHabitsByArea(areaId: string): Promise<DatabaseResult<any[]>>
  // Habit record operations
  createHabitRecord(data: CreateHabitRecordRequest): Promise<DatabaseResult<any>>
  updateHabitRecord(data: UpdateHabitRecordRequest): Promise<DatabaseResult<any>>
  getHabitRecords(habitId: string): Promise<DatabaseResult<any[]>>
  deleteHabitRecord(id: string): Promise<DatabaseResult<void>>
  // {{ AURA-X: Add - 定期维护任务API接口. Approval: 寸止(ID:1738157400). }}
  // Recurring task operations
  createRecurringTask(data: CreateRecurringTaskRequest): Promise<DatabaseResult<any>>
  getRecurringTasks(areaId: string): Promise<DatabaseResult<any[]>>
  updateRecurringTask(data: UpdateRecurringTaskRequest): Promise<DatabaseResult<any>>
  deleteRecurringTask(id: string): Promise<DatabaseResult<void>>
  // Resource linking operations
  getProjectResources(projectId: string): Promise<DatabaseResult<any[]>>
  linkResourceToProject(data: LinkResourceToProjectRequest): Promise<DatabaseResult<any>>
  unlinkResourceFromProject(resourceId: string, projectId: string): Promise<DatabaseResult<void>>
  getAreaResources(areaId: string): Promise<DatabaseResult<any[]>>
  unlinkResourceFromArea(resourceId: string, areaId: string): Promise<DatabaseResult<void>>
  createResource(data: CreateResourceRequest): Promise<DatabaseResult<any>>
  getResources(filters?: any): Promise<DatabaseResult<any[]>>

  // Document Link operations
  createDocumentLink(data: CreateDocumentLinkRequest): Promise<DatabaseResult<any>>
  getDocumentLinks(docPath: string): Promise<DatabaseResult<any>>
  getBacklinks(docPath: string): Promise<DatabaseResult<any[]>>
  getOutlinks(docPath: string): Promise<DatabaseResult<any[]>>
  updateDocumentLink(data: UpdateDocumentLinkRequest): Promise<DatabaseResult<any>>
  deleteDocumentLink(id: string): Promise<DatabaseResult<void>>
  deleteDocumentLinks(docPath: string): Promise<DatabaseResult<void>>
  updateDocumentPath(data: UpdateDocumentPathRequest): Promise<DatabaseResult<void>>
  markLinksAsInvalid(targetDocPath: string): Promise<DatabaseResult<void>>
  markLinksAsValid(targetDocPath: string): Promise<DatabaseResult<void>>
  getLinkStatistics(docPath: string): Promise<DatabaseResult<any>>
  searchLinks(data: SearchLinksRequest): Promise<DatabaseResult<any[]>>
  replaceDocumentLinks(data: ReplaceDocumentLinksRequest): Promise<DatabaseResult<any[]>>

  // Checklist operations
  createChecklist(data: CreateChecklistRequest): Promise<DatabaseResult<any>>
  getChecklists(areaId?: string): Promise<DatabaseResult<any[]>>
  updateChecklist(data: UpdateChecklistRequest): Promise<DatabaseResult<any>>
  deleteChecklist(id: string): Promise<DatabaseResult<void>>

  // Checklist Instance operations
  createChecklistInstance(data: CreateChecklistInstanceRequest): Promise<DatabaseResult<any>>
  getChecklistInstances(areaId?: string): Promise<DatabaseResult<any[]>>
  updateChecklistInstance(data: UpdateChecklistInstanceRequest): Promise<DatabaseResult<any>>
  deleteChecklistInstance(id: string): Promise<DatabaseResult<void>>
}

export interface FileSystemApi {
  initialize(): Promise<FileOperationResult<FileSystemConfig>>
  reinitialize(workspaceDirectory: string): Promise<FileOperationResult<FileSystemConfig>>
  readFile(data: ReadFileRequest): Promise<FileOperationResult<FileContent>>
  writeFile(data: WriteFileRequest): Promise<FileOperationResult<void>>
  deleteFile(path: string): Promise<FileOperationResult<void>>
  moveFile(data: MoveFileRequest): Promise<FileOperationResult<void>>
  copyFile(data: CopyFileRequest): Promise<FileOperationResult<void>>
  fileExists(path: string): Promise<FileOperationResult<boolean>>
  getFileInfo(path: string): Promise<FileOperationResult<FileInfo>>
  listDirectory(path: string): Promise<FileOperationResult<FileInfo[]>>
  createDirectory(path: string): Promise<FileOperationResult<void>>
  deleteDirectory(path: string): Promise<FileOperationResult<void>>
  rename(oldPath: string, newPath: string): Promise<FileOperationResult<void>>
  watchDirectory(path: string): Promise<FileOperationResult<void>>
  unwatchDirectory(path: string): Promise<FileOperationResult<void>>
}

export interface AppApi {
  getVersion(): Promise<string>
  getPath(name: string): Promise<string>
  showMessageBox(options: any): Promise<any>
  showErrorBox(title: string, content: string): Promise<void>
  showOpenDialog(options: any): Promise<any>
  showSaveDialog(options: any): Promise<any>
}

export interface WindowApi {
  minimize(): Promise<void>
  maximize(): Promise<void>
  close(): Promise<void>
  toggleDevTools(): Promise<void>
}

// Settings API interface
export interface SettingsApi {
  getUserSettings(): Promise<DatabaseResult<any>>
  updateUserSettings(settings: any): Promise<DatabaseResult<any>>
  resetUserSettings(): Promise<DatabaseResult<any>>
  exportData(): Promise<DatabaseResult<any>>
  importData(data: any): Promise<DatabaseResult<any>>
  createBackup(): Promise<DatabaseResult<any>>
  getDatabaseInfo(): Promise<DatabaseResult<any>>
  selectResourcePath(): Promise<DatabaseResult<string>>
  updateResourcePath(path: string): Promise<DatabaseResult<any>>
}

// Combined API interface
export interface ElectronApi {
  database: DatabaseApi
  fileSystem: FileSystemApi
  app: AppApi
  window: WindowApi
  settings: SettingsApi

  // Event listeners
  onFileSystemEvent(callback: (event: FileSystemEvent) => void): () => void
  onDatabaseEvent(callback: (event: any) => void): () => void
}

// Global type declaration for window.electronAPI
declare global {
  interface Window {
    electronAPI: ElectronApi
  }
}
