import { create } from 'zustand'
import { persist } from 'zustand/middleware'

export interface ShortcutConfig {
  id: string
  name: string
  description: string
  category: 'navigation' | 'editing' | 'project' | 'general'
  defaultKeys: string[]
  currentKeys: string[]
  enabled: boolean
}

interface ShortcutsState {
  shortcuts: ShortcutConfig[]
  isInitialized: boolean

  // Actions
  updateShortcut: (id: string, keys: string[]) => void
  resetShortcut: (id: string) => void
  toggleShortcut: (id: string, enabled: boolean) => void
  resetAllShortcuts: () => void
  getShortcutByKeys: (keys: string[]) => ShortcutConfig | undefined
  isKeyCombinationUsed: (keys: string[], excludeId?: string) => boolean
}

const defaultShortcuts: ShortcutConfig[] = [
  // 导航快捷键
  {
    id: 'nav-projects',
    name: '项目页面',
    description: '快速跳转到项目页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'P'],
    currentKeys: ['Ctrl', 'Shift', 'P'],
    enabled: true
  },
  {
    id: 'nav-areas',
    name: '领域页面',
    description: '快速跳转到领域页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'A'],
    currentKeys: ['Ctrl', 'Shift', 'A'],
    enabled: true
  },
  {
    id: 'nav-resources',
    name: '资源页面',
    description: '快速跳转到资源页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'R'],
    currentKeys: ['Ctrl', 'Shift', 'R'],
    enabled: true
  },
  {
    id: 'nav-archive',
    name: '归档页面',
    description: '快速跳转到归档页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', 'Shift', 'H'],
    currentKeys: ['Ctrl', 'Shift', 'H'],
    enabled: true
  },
  {
    id: 'nav-settings',
    name: '设置页面',
    description: '快速打开设置页面',
    category: 'navigation',
    defaultKeys: ['Ctrl', ','],
    currentKeys: ['Ctrl', ','],
    enabled: true
  },

  // 编辑快捷键
  {
    id: 'edit-save',
    name: '保存',
    description: '保存当前编辑内容',
    category: 'editing',
    defaultKeys: ['Ctrl', 'S'],
    currentKeys: ['Ctrl', 'S'],
    enabled: true
  },
  {
    id: 'edit-undo',
    name: '撤销',
    description: '撤销上一步操作',
    category: 'editing',
    defaultKeys: ['Ctrl', 'Z'],
    currentKeys: ['Ctrl', 'Z'],
    enabled: true
  },
  {
    id: 'edit-redo',
    name: '重做',
    description: '重做上一步操作',
    category: 'editing',
    defaultKeys: ['Ctrl', 'Y'],
    currentKeys: ['Ctrl', 'Y'],
    enabled: true
  },
  {
    id: 'edit-find',
    name: '查找',
    description: '打开查找对话框',
    category: 'editing',
    defaultKeys: ['Ctrl', 'F'],
    currentKeys: ['Ctrl', 'F'],
    enabled: true
  },

  // 项目管理快捷键
  {
    id: 'project-new',
    name: '新建项目',
    description: '快速创建新项目',
    category: 'project',
    defaultKeys: ['Ctrl', 'N'],
    currentKeys: ['Ctrl', 'N'],
    enabled: true
  },
  {
    id: 'project-search',
    name: '搜索项目',
    description: '打开项目搜索',
    category: 'project',
    defaultKeys: ['Ctrl', 'K'],
    currentKeys: ['Ctrl', 'K'],
    enabled: true
  },

  // 通用快捷键
  {
    id: 'general-help',
    name: '帮助',
    description: '打开帮助文档',
    category: 'general',
    defaultKeys: ['F1'],
    currentKeys: ['F1'],
    enabled: true
  },
  {
    id: 'general-refresh',
    name: '刷新',
    description: '刷新当前页面',
    category: 'general',
    defaultKeys: ['F5'],
    currentKeys: ['F5'],
    enabled: true
  }
]

export const useShortcutsStore = create<ShortcutsState>()(
  persist(
    (set, get) => ({
      shortcuts: defaultShortcuts,
      isInitialized: false,

      updateShortcut: (id, keys) => {
        set((state) => ({
          shortcuts: state.shortcuts.map(shortcut =>
            shortcut.id === id
              ? { ...shortcut, currentKeys: keys }
              : shortcut
          )
        }))
      },

      resetShortcut: (id) => {
        set((state) => ({
          shortcuts: state.shortcuts.map(shortcut =>
            shortcut.id === id
              ? { ...shortcut, currentKeys: [...shortcut.defaultKeys] }
              : shortcut
          )
        }))
      },

      toggleShortcut: (id, enabled) => {
        set((state) => ({
          shortcuts: state.shortcuts.map(shortcut =>
            shortcut.id === id
              ? { ...shortcut, enabled }
              : shortcut
          )
        }))
      },

      resetAllShortcuts: () => {
        set((state) => ({
          shortcuts: state.shortcuts.map(shortcut => ({
            ...shortcut,
            currentKeys: [...shortcut.defaultKeys],
            enabled: true
          }))
        }))
      },

      getShortcutByKeys: (keys) => {
        const { shortcuts } = get()
        return shortcuts.find(shortcut => 
          shortcut.enabled && 
          shortcut.currentKeys.length === keys.length &&
          shortcut.currentKeys.every((key, index) => key === keys[index])
        )
      },

      isKeyCombinationUsed: (keys, excludeId) => {
        const { shortcuts } = get()
        return shortcuts.some(shortcut => 
          shortcut.id !== excludeId &&
          shortcut.enabled &&
          shortcut.currentKeys.length === keys.length &&
          shortcut.currentKeys.every((key, index) => key === keys[index])
        )
      }
    }),
    {
      name: 'shortcuts-settings',
      partialize: (state) => ({ shortcuts: state.shortcuts })
    }
  )
)
