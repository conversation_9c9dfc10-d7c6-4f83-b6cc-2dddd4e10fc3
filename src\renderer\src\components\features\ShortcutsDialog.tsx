import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '../ui/dialog'
import { Button } from '../ui/button'
import { Input } from '../ui/input'
import { Label } from '../ui/label'
import { Switch } from '../ui/switch'
import { Badge } from '../ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../ui/tabs'
import { useShortcutsStore, type ShortcutConfig } from '../../store/shortcutsStore'
import { useLanguage } from '../../contexts/LanguageContext'
import { useUIStore } from '../../store/uiStore'

interface ShortcutsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function ShortcutsDialog({ open, onOpenChange }: ShortcutsDialogProps) {
  const { t } = useLanguage()
  const { addNotification } = useUIStore()
  const { 
    shortcuts, 
    updateShortcut, 
    resetShortcut, 
    toggleShortcut, 
    resetAllShortcuts,
    isKeyCombinationUsed 
  } = useShortcutsStore()

  const [editingShortcut, setEditingShortcut] = useState<string | null>(null)
  const [recordingKeys, setRecordingKeys] = useState<string[]>([])
  const [isRecording, setIsRecording] = useState(false)

  // 按类别分组快捷键
  const groupedShortcuts = shortcuts.reduce((groups, shortcut) => {
    if (!groups[shortcut.category]) {
      groups[shortcut.category] = []
    }
    groups[shortcut.category].push(shortcut)
    return groups
  }, {} as Record<string, ShortcutConfig[]>)

  // 键盘事件处理
  useEffect(() => {
    if (!isRecording) return

    const handleKeyDown = (e: KeyboardEvent) => {
      e.preventDefault()
      e.stopPropagation()

      const keys: string[] = []
      if (e.ctrlKey) keys.push('Ctrl')
      if (e.altKey) keys.push('Alt')
      if (e.shiftKey) keys.push('Shift')
      if (e.metaKey) keys.push('Meta')

      // 添加主键
      if (e.key !== 'Control' && e.key !== 'Alt' && e.key !== 'Shift' && e.key !== 'Meta') {
        keys.push(e.key.length === 1 ? e.key.toUpperCase() : e.key)
      }

      if (keys.length > 0) {
        setRecordingKeys(keys)
      }
    }

    const handleKeyUp = (e: KeyboardEvent) => {
      if (recordingKeys.length > 0) {
        setIsRecording(false)
        
        // 检查是否与现有快捷键冲突
        if (editingShortcut && isKeyCombinationUsed(recordingKeys, editingShortcut)) {
          addNotification({
            type: 'error',
            title: t('settings.shortcuts.conflictTitle'),
            message: t('settings.shortcuts.conflictMessage')
          })
          setRecordingKeys([])
          setEditingShortcut(null)
          return
        }

        // 更新快捷键
        if (editingShortcut) {
          updateShortcut(editingShortcut, recordingKeys)
          addNotification({
            type: 'success',
            title: t('settings.shortcuts.updateSuccess'),
            message: t('settings.shortcuts.updateSuccessMessage')
          })
        }

        setRecordingKeys([])
        setEditingShortcut(null)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    document.addEventListener('keyup', handleKeyUp)

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('keyup', handleKeyUp)
    }
  }, [isRecording, recordingKeys, editingShortcut, updateShortcut, isKeyCombinationUsed, addNotification, t])

  const startRecording = (shortcutId: string) => {
    setEditingShortcut(shortcutId)
    setIsRecording(true)
    setRecordingKeys([])
  }

  const cancelRecording = () => {
    setIsRecording(false)
    setEditingShortcut(null)
    setRecordingKeys([])
  }

  const handleReset = (shortcutId: string) => {
    resetShortcut(shortcutId)
    addNotification({
      type: 'success',
      title: t('settings.shortcuts.resetSuccess'),
      message: t('settings.shortcuts.resetSuccessMessage')
    })
  }

  const handleResetAll = () => {
    resetAllShortcuts()
    addNotification({
      type: 'success',
      title: t('settings.shortcuts.resetAllSuccess'),
      message: t('settings.shortcuts.resetAllSuccessMessage')
    })
  }

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'navigation': return t('settings.shortcuts.categories.navigation')
      case 'editing': return t('settings.shortcuts.categories.editing')
      case 'project': return t('settings.shortcuts.categories.project')
      case 'general': return t('settings.shortcuts.categories.general')
      default: return category
    }
  }

  const renderShortcutItem = (shortcut: ShortcutConfig) => {
    const isEditing = editingShortcut === shortcut.id
    const displayKeys = isEditing && isRecording ? recordingKeys : shortcut.currentKeys

    return (
      <div key={shortcut.id} className="flex items-center justify-between p-4 border rounded-lg">
        <div className="flex-1">
          <div className="flex items-center gap-2">
            <h4 className="font-medium">{shortcut.name}</h4>
            <Switch
              checked={shortcut.enabled}
              onCheckedChange={(enabled) => toggleShortcut(shortcut.id, enabled)}
            />
          </div>
          <p className="text-sm text-muted-foreground">{shortcut.description}</p>
        </div>
        
        <div className="flex items-center gap-2">
          <div className="flex gap-1">
            {displayKeys.map((key, index) => (
              <Badge key={index} variant="outline" className="text-xs">
                {key}
              </Badge>
            ))}
            {isEditing && isRecording && (
              <Badge variant="secondary" className="text-xs animate-pulse">
                {t('settings.shortcuts.recording')}
              </Badge>
            )}
          </div>
          
          <div className="flex gap-1">
            {isEditing ? (
              <Button size="sm" variant="outline" onClick={cancelRecording}>
                {t('settings.shortcuts.cancel')}
              </Button>
            ) : (
              <>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => startRecording(shortcut.id)}
                  disabled={!shortcut.enabled}
                >
                  {t('settings.shortcuts.edit')}
                </Button>
                <Button 
                  size="sm" 
                  variant="ghost" 
                  onClick={() => handleReset(shortcut.id)}
                  disabled={!shortcut.enabled}
                >
                  {t('settings.shortcuts.reset')}
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{t('settings.shortcuts.title')}</DialogTitle>
          <DialogDescription>
            {t('settings.shortcuts.description')}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {t('settings.shortcuts.instructions')}
            </p>
            <Button variant="outline" onClick={handleResetAll}>
              {t('settings.shortcuts.resetAll')}
            </Button>
          </div>

          <Tabs defaultValue="navigation" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="navigation">{getCategoryName('navigation')}</TabsTrigger>
              <TabsTrigger value="editing">{getCategoryName('editing')}</TabsTrigger>
              <TabsTrigger value="project">{getCategoryName('project')}</TabsTrigger>
              <TabsTrigger value="general">{getCategoryName('general')}</TabsTrigger>
            </TabsList>

            {Object.entries(groupedShortcuts).map(([category, shortcuts]) => (
              <TabsContent key={category} value={category} className="space-y-2">
                {shortcuts.map(renderShortcutItem)}
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </DialogContent>
    </Dialog>
  )
}
